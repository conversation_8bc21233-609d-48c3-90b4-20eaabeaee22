import { UserProfile } from '@clerk/nextjs';
//import { roRO } from '@clerk/localizations'; // Import Romanian localization
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { CustomPreferencesForm } from '@/app/components/account/settings/CustomPreferencesForm';


// The '[[...user-profile]]' slug from the URL determines which tab is active.
// For example, /user-profile/security will open the 'security' tab.
export default async function UserProfilePage({
  params,
}: {
   params: Promise<{ 'user-profile'?: string[] } >;
}) {
  // Await the params promise to get the actual parameters object
  const resolvedParams = await params;

  // Now you can safely access properties on resolvedParams
  const activeTab = resolvedParams['user-profile']?.[0] || 'account';

  return (
    <div className="min-h-screen flex flex-col items-center p-4 pt-12">
      <Tabs defaultValue={activeTab} className="w-full max-w-5xl">
        <TabsList className="grid w-full grid-cols-3">
          {/* These triggers navigate using the URL */}
          <TabsTrigger value="account" asChild>
            <a href="/user-profile">Cont</a>
          </TabsTrigger>
          <TabsTrigger value="security" asChild>
            <a href="/user-profile/security">Securitate</a>
          </TabsTrigger>
          <TabsTrigger value="preferences">Preferințe</TabsTrigger>
        </TabsList>
        
        {/* Content for Clerk's Profile and Security */}
        <TabsContent value="account">
          <UserProfileContent />
        </TabsContent>
        <TabsContent value="security">
          <UserProfileContent />
        </TabsContent>
        
        {/* Content for our custom preferences tab */}
        <TabsContent value="preferences">
          <div className="p-6 border rounded-lg mt-4 bg-card text-card-foreground">
             <CustomPreferencesForm />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// We create a separate component for UserProfile to keep the main component clean
function UserProfileContent() {
  return (
    <div className="mt-4">
      <UserProfile
        path="/user-profile" // Base path for Clerk's internal routing
        routing="path"       // Tells Clerk to use the URL for routing
        //localization={roRO}  // Apply Romanian language pack
        appearance={{
          variables: {
            // Use our CSS variables for theming
            // colorBackground: 'var(--clerk-bg)',
            // colorInputBackground: 'var(--clerk-bg)',
            // colorText: 'var(--clerk-text)',
          },
          elements: {
            // General styling
            rootBox: 'w-full',
            card: 'shadow-none border rounded-lg w-full ', //bg-[var(--clerk-card-bg)]
            navbar: 'hidden', // We hide Clerk's navbar because we use our own Tabs
            pageScrollBox: 'py-0',
            headerTitle: 'hidden', // Hide Clerk's title
            headerSubtitle: 'hidden', // Hide Clerk's subtitle
          },
        }}
      />
    </div>
  );
}



// import { UserProfile } from '@clerk/nextjs';

// export default function UserProfilePage() {
//   return (
//     <div className="min-h-screen flex items-center justify-center p-4">
//       <UserProfile 
//         path="/user-profile"
//         routing="path"
//         appearance={{
//           elements: {
//             rootBox: "mx-auto",
//             card: "shadow-lg"
//           }
//         }}
//       />
//     </div>
//   );
// }
// 'use client'

// import { UserProfile } from '@clerk/nextjs'

// const DotIcon = () => {
//   return (
//     <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">
//       <path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512z" />
//     </svg>
//   )
// }

// const CustomPage = () => {
//   return (
//     <div>
//       <h1>Custom page</h1>
//       <p>This is the content of the custom page.</p>
//     </div>
//   )
// }

// const UserProfilePage = () => (
//   <UserProfile>
//     <UserProfile.Page label="Custom Page" url="custom" labelIcon={<DotIcon />}>
//       <CustomPage />
//     </UserProfile.Page>
//     <UserProfile.Link label="Homepage" url="/" labelIcon={<DotIcon />} />
//     <UserProfile.Page label="account" />
//     <UserProfile.Page label="security" />
//   </UserProfile>
// )

// export default UserProfilePage