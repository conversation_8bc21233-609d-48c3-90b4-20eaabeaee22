"use client";

import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, AlertCircle, Mail } from "lucide-react";
import { ClerkProfileInput, clerkProfileSchema } from "@/lib/zod";
import { toast } from "sonner";
import { useUser } from "@clerk/nextjs";
import { AccountSettingsData } from "@/app/getData/account-settings";
import { getClerkErrorMessage } from "@/lib/password";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ConfirmDialog } from "./ConfirmDialog";

interface ProfileFormProps {
  accountData: AccountSettingsData;
  onEmailVerificationPending?: () => void;
  ssoProvider: string | null;
  isEmailVerificationPending?: boolean;
}

export default function ProfileForm({ 
  accountData, 
  onEmailVerificationPending,
  ssoProvider,
  isEmailVerificationPending = false 
}: ProfileFormProps) {
  const [isPending, startTransition] = useTransition();
  const [showEmailConfirm, setShowEmailConfirm] = useState(false);
  const [pendingEmailChange, setPendingEmailChange] = useState<string>("");
  const { user } = useUser();

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty, dirtyFields }
  } = useForm<ClerkProfileInput>({
    resolver: zodResolver(clerkProfileSchema),
    defaultValues: {
      firstName: accountData.firstName || user?.firstName || '',
      lastName: accountData.lastName || user?.lastName || '',
      email: accountData.email || user?.primaryEmailAddress?.emailAddress || '',
    }
  });

  const handleEmailChange = async () => {
    if (!user) return;
    
    setShowEmailConfirm(false);
    
    startTransition(async () => {
      try {
        // Check if email already exists in user's emails
        const emailExists = user.emailAddresses.some(e => e.emailAddress === pendingEmailChange);
        if (emailExists) {
          toast.error("Această adresă de email este deja asociată cu contul tău.");
          return;
        }

        // Create the new email address
        const newEmailAddress = await user.createEmailAddress({ email: pendingEmailChange });
        
        // Send a verification code to the new email
        await newEmailAddress.prepareVerification({ strategy: 'email_code' });
        
        // Notify parent component
        onEmailVerificationPending?.();
        
        toast.info(
          "Un cod de verificare a fost trimis la noua adresă de email. " +
          "Email-ul va fi actualizat după verificare.",
          { duration: 6000 }
        );
      } catch (error: any) {
        if (error.errors?.[0]?.code === 'form_identifier_exists') {
          toast.error("Această adresă de email este deja folosită de alt cont.");
        } else {
          toast.error(getClerkErrorMessage(error));
        }
      }
    });
  };

  const onSubmit = (data: ClerkProfileInput) => {
    if (!user) return;

    // Handle email change with dialog
    if (dirtyFields.email && !isEmailVerificationPending) {
      const newEmail = data.email;
      const primaryEmail = user.primaryEmailAddress;

      if (newEmail !== primaryEmail?.emailAddress) {
        setPendingEmailChange(newEmail);
        setShowEmailConfirm(true);
        return; // Don't continue with the rest of the form submission
      }
    }

    // Handle name updates
    startTransition(async () => {
      let promises = [];
      let successMessages = [];

      if (dirtyFields.firstName || dirtyFields.lastName) {
        promises.push(user.update({ firstName: data.firstName, lastName: data.lastName }));
        successMessages.push("Informațiile personale au fost actualizate.");
      }

      try {
        await Promise.all(promises);
        if (successMessages.length > 0) {
          toast.success(successMessages.join(' '));
        }
      } catch (error) {
        toast.error(getClerkErrorMessage(error));
      }
    });
  };

  const primaryEmail = user?.primaryEmailAddress?.emailAddress || '';

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Informații Personale</CardTitle>
          <CardDescription>
            Aceste informații sunt gestionate de sistemul de autentificare.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isEmailVerificationPending && (
            <Alert className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                O verificare de email este în curs. Finalizează verificarea înainte de a face alte modificări.
              </AlertDescription>
            </Alert>
          )}
          
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
            {/* Form fields remain the same */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="md:col-span-2 space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">Prenume *</Label>
                    <Input
                      id="firstName"
                      {...register("firstName")}
                      className={errors.firstName ? "border-red-500" : ""}
                      disabled={isPending}
                    />
                    {errors.firstName && (
                      <p className="text-sm text-red-600">{errors.firstName.message}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Nume de familie *</Label>
                    <Input
                      id="lastName"
                      {...register("lastName")}
                      className={errors.lastName ? "border-red-500" : ""}
                      disabled={isPending}
                    />
                    {errors.lastName && (
                      <p className="text-sm text-red-600">{errors.lastName.message}</p>
                    )}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">
                    Email * 
                    {dirtyFields.email && (
                      <span className="text-sm text-gray-500 ml-2">
                        (necesită verificare)
                      </span>
                    )}
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    {...register("email")}
                    className={errors.email ? "border-red-500" : ""}
                    disabled={isPending || isEmailVerificationPending}
                  />
                  {errors.email && (
                    <p className="text-sm text-red-600">{errors.email.message}</p>
                  )}
                </div>
              </div>
            </div>
            <div className="flex justify-end pt-4 border-t">
              <Button
                type="submit"
                disabled={isPending || !isDirty || isEmailVerificationPending}
                className="bg-[#0066B1] hover:bg-[#004d85] text-white"
              >
                {isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Se actualizează...
                  </>
                ) : (
                  "Actualizează informațiile personale"
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Email Change Confirmation Dialog */}
      <ConfirmDialog
        open={showEmailConfirm}
        onOpenChange={setShowEmailConfirm}
        title="Confirmare schimbare email"
        onConfirm={handleEmailChange}
        onCancel={() => setShowEmailConfirm(false)}
        confirmText="Schimbă email-ul"
        cancelText="Anulează"
      >
        <div className="space-y-4">
          <div className="flex items-center space-x-2 text-sm">
            <Mail className="h-4 w-4 text-gray-500" />
            <span className="text-gray-600">Email actual:</span>
            <span className="font-medium">{primaryEmail}</span>
          </div>
          <div className="flex items-center space-x-2 text-sm">
            <Mail className="h-4 w-4 text-[#0066B1]" />
            <span className="text-gray-600">Email nou:</span>
            <span className="font-medium text-[#0066B1]">{pendingEmailChange}</span>
          </div>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Vei primi un cod de verificare la noua adresă de email. 
              Email-ul va fi schimbat doar după ce introduci codul corect.
            </AlertDescription>
          </Alert>
        </div>
      </ConfirmDialog>
    </div>
  );
}


// "use client";

// import { useState, useTransition } from "react";
// import { useForm } from "react-hook-form";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Label } from "@/components/ui/label";
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
// import { AlertCircle, Loader2 } from "lucide-react";
// import { ClerkProfileInput, clerkProfileSchema } from "@/lib/zod";
// import { toast } from "sonner";
// import { useUser } from "@clerk/nextjs";
// import { AccountSettingsData } from "@/app/getData/account-settings";
// import { getClerkErrorMessage } from "@/lib/password";
// import { Alert, AlertDescription } from "@/components/ui/alert";

// interface ProfileFormProps {
//   accountData: AccountSettingsData;
//   onEmailVerificationPending?: (email: string) => void;
//   isEmailVerificationPending?: boolean;
//   ssoProvider: string | null;
// }

// export default function ProfileForm({ accountData, onEmailVerificationPending, isEmailVerificationPending = false  }: ProfileFormProps) {
//   const [isPending, startTransition] = useTransition();
//   const { user } = useUser();

//   const {
//     register,
//     handleSubmit,
//     formState: { errors, isDirty, dirtyFields }
//   } = useForm<ClerkProfileInput>({
//     resolver: zodResolver(clerkProfileSchema),
//     defaultValues: {
//       firstName: accountData.firstName || user?.firstName || '',
//       lastName: accountData.lastName || user?.lastName || '',
//       email: accountData.email || user?.emailAddresses[0]?.emailAddress || '',
//     }
//   });

//   const onSubmit = (data: ClerkProfileInput) => {
//     if (!user) return;

//     startTransition(async () => {
//       let promises = [];
//       let successMessages = [];

//       // 1. Handle Name Update
//       if (dirtyFields.firstName || dirtyFields.lastName) {
//         promises.push(user.update({ firstName: data.firstName, lastName: data.lastName }));
//         successMessages.push("Informațiile personale au fost actualizate.");
//       }

//       // 2. Handle Email Update
//       if (dirtyFields.email && !isEmailVerificationPending) {
//         const newEmail = data.email;
//         const primaryEmail = user.emailAddresses[0];

//         if (newEmail !== primaryEmail?.emailAddress) {
//           try {
//             // Check if email already exists in user's emails
//             const emailExists = user.emailAddresses.some(e => e.emailAddress === newEmail);
//             if (emailExists) {
//               toast.error("Această adresă de email este deja asociată cu contul tău.");
//               return;
//             }

//             // Show confirmation dialog
//             const confirmChange = window.confirm(
//               `Ești sigur că vrei să schimbi adresa de email?\n\n` +
//               `De la: ${primaryEmail?.emailAddress}\n` +
//               `La: ${newEmail}\n\n` +
//               `Vei primi un cod de verificare la noua adresă.`
//             );

//             if (!confirmChange) return;

//             // Create the new email address
//             const newEmailAddress = await user.createEmailAddress({ email: newEmail });
            
//             // Send a verification code to the new email
//             await newEmailAddress.prepareVerification({ strategy: 'email_code' });

//              // Notify parent component
//             onEmailVerificationPending?.(newEmail);
            
//             toast.info("Un cod de verificare a fost trimis la noua ta adresă de email. Email-ul va fi actualizat după verificare.", {
//               duration: 4000
//             });
//             return; 
//           } catch (error) {
//             toast.error(getClerkErrorMessage(error));
//             return;
//           }
//         }
//       }

//       try {
//         await Promise.all(promises);
//         if (successMessages.length > 0) {
//           toast.success(successMessages.join(' '));
//         }
//       } catch (error) {
//         toast.error(getClerkErrorMessage(error));
//       }
//     });
//   };

//   return (
//     <div className="space-y-6">
//       <Card>
//         <CardHeader>
//           <CardTitle>Informații Personale</CardTitle>
//           <CardDescription>
//             Aceste informații sunt gestionate de sistemul de autentificare.
//           </CardDescription>
//         </CardHeader>
//         <CardContent>

//           {/* Email Verification Pending Alert */}
//           {isEmailVerificationPending && (
//             <Alert className="mb-4">
//               <AlertCircle className="h-4 w-4" />
//               <AlertDescription>
//                 O verificare de email este în curs. Finalizează verificarea înainte de a face alte modificări.
//               </AlertDescription>
//             </Alert>
//           )}

//           {/* Profile Form */}
//           <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
//             <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
//               <div className="md:col-span-2 space-y-4">
//                 <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
//                   <div className="space-y-2">
//                     <Label htmlFor="firstName">Prenume *</Label>
//                     <Input
//                       id="firstName"
//                       {...register("firstName")}
//                       className={errors.firstName ? "border-red-500" : ""}
//                       disabled={isPending}
//                     />
//                     {errors.firstName && (
//                       <p className="text-sm text-red-600">{errors.firstName.message}</p>
//                     )}
//                   </div>
//                   <div className="space-y-2">
//                     <Label htmlFor="lastName">Nume de familie *</Label>
//                     <Input
//                       id="lastName"
//                       {...register("lastName")}
//                       className={errors.lastName ? "border-red-500" : ""}
//                       disabled={isPending}
//                     />
//                     {errors.lastName && (
//                       <p className="text-sm text-red-600">{errors.lastName.message}</p>
//                     )}
//                   </div>
//                 </div>
//                 <div className="space-y-2">
//                   <Label htmlFor="email">Email *</Label>
//                   <Input
//                     id="email"
//                     type="email"
//                     {...register("email")}
//                     className={errors.email ? "border-red-500" : ""}
//                     disabled={isPending}
//                   />
//                   {errors.email && (
//                     <p className="text-sm text-red-600">{errors.email.message}</p>
//                   )}
//                 </div>
//               </div>
//             </div>
//             <div className="flex justify-end pt-4 border-t">
//               <Button
//                 type="submit"
//                 disabled={isPending || !isDirty || isEmailVerificationPending}
//                 className="bg-[#0066B1] hover:bg-[#004d85] text-white"
//               >
//                 {isPending ? (
//                   <>
//                     <Loader2 className="mr-2 h-4 w-4 animate-spin" />
//                     Se actualizează...
//                   </>
//                 ) : (
//                   "Actualizează informațiile personale"
//                 )}
//               </Button>
//             </div>
//           </form>
//         </CardContent>
//       </Card>
//     </div>
//   );
// }

