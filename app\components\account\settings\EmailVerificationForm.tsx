

//claude
"use client";

import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, X, AlertCircle, Shield } from "lucide-react";
import { toast } from "sonner";
import { useUser, useReverification } from "@clerk/nextjs";
import { isClerkRuntimeError, isReverificationCancelledError } from "@clerk/nextjs/errors";
import { getClerkErrorMessage, isEmailVerificationError } from "@/lib/password";
import { EmailVerificationInput, emailVerificationSchema } from "@/lib/zod";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface EmailVerificationFormProps {
  onVerificationComplete?: () => void;
  onVerificationCancelled?: () => void;
}

// Verification flow states
type VerificationState =
  | 'initial'           // Initial state - ready to verify email code
  | 'verifying'         // Verifying the email code
  | 'verified'          // Email verified, attempting to set as primary
  | 'reverification'    // Session reverification required
  | 'updating'          // Updating primary email after reverification
  | 'complete';         // Process complete

export default function EmailVerificationForm({
  onVerificationComplete,
  onVerificationCancelled
}: EmailVerificationFormProps) {
  const [isPending, startTransition] = useTransition();
  const [isResending, startResending] = useTransition();
  const [isCancelling, startCancelling] = useTransition();
  const [attemptCount, setAttemptCount] = useState(0);
  const [verificationState, setVerificationState] = useState<VerificationState>('initial');


  const { user } = useUser();



  // Enhanced function to update primary email with reverification support
  const updatePrimaryEmail = useReverification(
    async (emailId: string) => {
      if (!user) return;
      return await user.update({ primaryEmailAddressId: emailId });
    }
  );

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    reset
  } = useForm<EmailVerificationInput>({
    resolver: zodResolver(emailVerificationSchema),
  });

    const unverifiedEmail = user?.emailAddresses.find(
    (email) => email.verification.status === "unverified" && 
    email.verification.strategy === "email_code"
  );

  const onSubmit = (data: EmailVerificationInput) => {
    if (!user || !unverifiedEmail) return;

    startTransition(async () => {
      try {
        setVerificationState('verifying');

        // Step 1: Verify the email code
        const result = await unverifiedEmail.attemptVerification({
          code: data.code.trim().toUpperCase()
        });

        if (result.verification.status === "verified") {
          setVerificationState('verified');

          try {
            // Step 2: Try to set as primary email (may require reverification)
            setVerificationState('updating');

            // Use the reverification-wrapped function
            const result = await updatePrimaryEmail(unverifiedEmail.id);

            if (result) {
              // Step 3: Success - reload user and clean up
              await user.reload();

              // Delete old email addresses (keep only the new primary)
              const otherEmails = user.emailAddresses.filter(
                email => email.id !== unverifiedEmail.id
              );

              for (const email of otherEmails) {
                try {
                  await email.destroy();
                } catch (deleteError) {
                  console.warn("Failed to delete old email:", deleteError);
                }
              }

              setVerificationState('complete');
              toast.success("Email verificat și actualizat cu succes!");
              onVerificationComplete?.();
            } else {
              // Reverification was required and handled by the hook
              setVerificationState('reverification');
              toast.info(
                "Din motive de securitate, trebuie să îți confirmi identitatea pentru a schimba email-ul principal.",
                { duration: 6000 }
              );
            }

          } catch (updateError: any) {
            // Handle other errors
            setVerificationState('initial');
            toast.error(getClerkErrorMessage(updateError));
          }
        }
      } catch (error: any) {
        setAttemptCount(prev => prev + 1);
        setVerificationState('initial');
        reset();

        if (isEmailVerificationError(error)) {
          const errorCode = error.errors[0].code;

          if (errorCode === 'verification_failed' || errorCode === 'form_code_incorrect') {
            const remainingAttempts = Math.max(0, 5 - attemptCount - 1);
            if (remainingAttempts > 0) {
              toast.error(`Cod invalid. Mai ai ${remainingAttempts} încercări.`);
            } else {
              toast.error("Ai depășit numărul maxim de încercări. Solicită un cod nou.");
            }
          } else if (errorCode === 'verification_expired') {
            toast.error("Codul a expirat. Te rugăm să soliciți unul nou.");
          }
        } else {
          toast.error(getClerkErrorMessage(error));
        }
      }
    });
  };



  const onResendCode = () => {
    if (!unverifiedEmail) return;

    startResending(async () => {
      try {
        await unverifiedEmail.prepareVerification({ strategy: 'email_code' });
        setAttemptCount(0);
        reset();
        toast.info("Un nou cod de verificare a fost trimis la adresa de email.");
      } catch (error: any) {
        console.error("Error resending verification email:", error);
        const clerkError = error.errors?.[0];
        toast.error(
          clerkError?.longMessage || 
          clerkError?.message || 
          "A apărut o eroare la retrimiterea codului de verificare."
        );
      }
    });
  };

  const onCancelVerification = () => {
    if (!unverifiedEmail) return;
    
    const confirm = window.confirm(
      "Ești sigur că vrei să anulezi schimbarea de email? " +
      "Vei rămâne cu adresa actuală."
    );
    if (!confirm) return;
    
    startCancelling(async () => {
      try {
        await unverifiedEmail.destroy();
        toast.info("Schimbarea de email a fost anulată.");
        onVerificationCancelled?.();
      } catch (error) {
        toast.error("Nu s-a putut anula schimbarea.");
      }
    });
  };

  if (!unverifiedEmail) {
    return null;
  }



  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>
              {verificationState === 'verified' ? 'Email Verificat' :
               verificationState === 'updating' ? 'Actualizare Email...' :
               verificationState === 'complete' ? 'Finalizat' : 'Verificare Email'}
            </CardTitle>
            <CardDescription className="mt-2">
              {verificationState === 'complete' ? (
                <span className="text-green-600 font-medium">
                  Email-ul a fost verificat și actualizat cu succes!
                </span>
              ) : verificationState === 'updating' ? (
                <span className="text-blue-600">
                  Se actualizează email-ul principal...
                </span>
              ) : (
                <>
                  Un cod de verificare a fost trimis la: <strong>{unverifiedEmail.emailAddress}</strong>
                  <br />
                  <span className="text-sm text-gray-500">
                    Codul este format din 6 caractere și expiră în 10 minute.
                  </span>
                </>
              )}
            </CardDescription>
          </div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={onCancelVerification}
                disabled={isCancelling || verificationState === 'updating' || verificationState === 'complete'}
                title="Anulează schimbarea"
              >
                <X className="h-7 w-7 " />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Anuleaza schimbarea</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </CardHeader>
      <CardContent>
        {attemptCount >= 3 && verificationState === 'initial' && (
          <Alert className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-sm text-orange-500">
              Ai {5 - attemptCount} încercări rămase. După aceea va trebui să soliciți un cod nou.
            </AlertDescription>
          </Alert>
        )}

        {verificationState === 'verified' && (
          <Alert className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-blue-600">
              Email-ul a fost verificat cu succes. Se actualizează ca email principal...
            </AlertDescription>
          </Alert>
        )}

        {verificationState === 'complete' ? (
          <div className="text-center py-4">
            <div className="text-green-600 font-medium mb-2">
              ✅ Email actualizat cu succes!
            </div>
            <p className="text-sm text-gray-500">
              Poți închide această fereastră.
            </p>
          </div>
        ) : (
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="code">Cod de verificare *</Label>
              <Input
                id="code"
                {...register("code")}
                placeholder="123456"
                className={errors.code ? "border-red-500" : ""}
                disabled={isPending || verificationState !== 'initial'}
                maxLength={10}
              />
              {errors.code && (
                <p className="text-sm text-red-600">{errors.code.message}</p>
              )}
            </div>
            <div className="flex justify-between">
              <Button
                type="button"
                onClick={onResendCode}
                disabled={isResending || verificationState !== 'initial'}
                variant="outline"
              >
                {isResending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Se retrimite...
                  </>
                ) : (
                  "Retrimite codul"
                )}
              </Button>

              <Button
                type="submit"
                disabled={isPending || !isDirty || attemptCount >= 5 || verificationState !== 'initial'}
                className="bg-[#0066B1] hover:bg-[#004d85] text-white"
              >
                {isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {verificationState === 'verifying' ? 'Se verifică...' :
                     verificationState === 'updating' ? 'Se actualizează...' : 'Se procesează...'}
                  </>
                ) : (
                  "Verifică codul"
                )}
              </Button>
            </div>
          </form>
        )}
      </CardContent>
    </Card>
  );
}







//code without the clerk hook useReverification
// "use client";

// import { useState, useTransition, useEffect } from "react";
// import { useForm } from "react-hook-form";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Label } from "@/components/ui/label";
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
// import { Loader2, X } from "lucide-react";
// import { toast } from "sonner";
// import { useUser } from "@clerk/nextjs";
// import { getClerkErrorMessage } from "@/lib/password";
// import { EmailVerificationInput, emailVerificationSchema } from "@/lib/zod";
// import { Alert, AlertDescription } from "@/components/ui/alert";

// interface EmailVerificationFormProps {
//   onVerificationComplete?: () => void;
//   onVerificationCancelled?: () => void;
// }

// export default function EmailVerificationForm({ 
//   onVerificationComplete,
//   onVerificationCancelled 
// }: EmailVerificationFormProps) {
//   const [isPending, startTransition] = useTransition();
//   const [isResending, startResending] = useTransition();
//   const [isCancelling, startCancelling] = useTransition();
//   const [attemptCount, setAttemptCount] = useState(0);
//   const { user } = useUser();

//   const {
//     register,
//     handleSubmit,
//     formState: { errors, isDirty },
//     reset
//   } = useForm<EmailVerificationInput>({
//     resolver: zodResolver(emailVerificationSchema),
//   });

//   const unverifiedEmail = user?.emailAddresses.find(
//     (email) => email.verification.status === "unverified" && 
//     email.verification.strategy === "email_code"
//   );

//   //claude
//   const onSubmit = (data: EmailVerificationInput) => {
//     if (!user || !unverifiedEmail) return;

//     startTransition(async () => {
//       try {
//         const result = await unverifiedEmail.attemptVerification({ 
//           code: data.code.trim().toUpperCase() 
//         });

//         if (result.verification.status === "verified") {
//           try {
//             // Try to set as primary email
//             await user.update({ primaryEmailAddressId: unverifiedEmail.id });
            
//             // Reload user to get fresh data
//             await user.reload();
            
//             // Delete the old email
//             const previousPrimaryEmail = user.emailAddresses.find(
//               email => email.id !== unverifiedEmail.id && 
//               email.verification.status === "verified"
//             );

//             if (previousPrimaryEmail) {
//               try {
//                 await previousPrimaryEmail.destroy();
//               } catch (deleteError) {
//                 console.error("Failed to delete old email:", deleteError);
//               }
//             }
            
//             toast.success("Email verificat și actualizat cu succes!");
//             onVerificationComplete?.();
            
//           } catch (updateError: any) {
//             // Handle session re-verification required
//             const errorCode = updateError.errors?.[0]?.code;
            
//             if (errorCode === 'session_reverification_required' || 
//                 errorCode === 'session_verification_required') {
//               // Email is verified but can't be set as primary due to session
//               toast.warning(
//                 "Email-ul a fost verificat, dar sesiunea necesită re-autentificare. " +
//                 "Te rugăm să te deconectezi și să te reconectezi pentru a finaliza schimbarea.",
//                 { duration: 8000 }
//               );
              
//               // Still call onVerificationComplete to update UI
//               onVerificationComplete?.();
//             } else {
//               throw updateError; // Re-throw other errors
//             }
//           }
//         }
//       } catch (error: any) {
//         setAttemptCount(prev => prev + 1);
//         reset();
        
//         const errorCode = error.errors?.[0]?.code;
        
//         if (errorCode === 'verification_failed' || errorCode === 'form_code_incorrect') {
//           const remainingAttempts = Math.max(0, 5 - attemptCount - 1);
//           if (remainingAttempts > 0) {
//             toast.error(`Cod invalid. Mai ai ${remainingAttempts} încercări.`);
//           } else {
//             toast.error("Ai depășit numărul maxim de încercări. Solicită un cod nou.");
//           }
//         } else if (errorCode === 'verification_expired') {
//           toast.error("Codul a expirat. Te rugăm să soliciți unul nou.");
//         } else {
//           toast.error(getClerkErrorMessage(error));
//         }
//       }
//     });
//   };

//   //chatgpt
// //   const onSubmit = (data: EmailVerificationInput) => {
// //   if (!user || !unverifiedEmail) return;

// //   startTransition(async () => {
// //     try {
// //       const result = await unverifiedEmail.attemptVerification({
// //         code: data.code.trim().toUpperCase(),
// //       });

// //       if (result.verification.status === "verified") {
// //         try {
// //           // 🛡️ Try updating primary email
// //           await user.update({ primaryEmailAddressId: unverifiedEmail.id });

// //           // ✅ Reload the user after update succeeds
// //           await user.reload();

// //           // 🗑️ Delete ALL other email addresses (except this one)
// //           const otherEmails = user.emailAddresses.filter(
// //             email => email.id !== unverifiedEmail.id
// //           );

// //           for (const email of otherEmails) {
// //             try {
// //               await email.destroy();
// //             } catch (err) {
// //               console.warn("Email deletion failed:", err);
// //               toast.warning("Emailul vechi nu a putut fi șters automat.");
// //             }
// //           }

// //           toast.success("Emailul a fost verificat și actualizat cu succes!");
// //           onVerificationComplete?.();
// //         } catch (updateError: any) {
// //           const errorCode = updateError?.errors?.[0]?.code;

// //           if (errorCode === "session_reverification_required") {
// //             toast.error("Sesiunea necesită o re-verificare. Te rugăm să te deconectezi și să te conectezi din nou pentru a finaliza schimbarea adresei de email.");
// //           } else {
// //             toast.error(getClerkErrorMessage(updateError));
// //           }

// //           // Do not call onVerificationComplete — let the user fix the session first
// //         }
// //       }
// //     } catch (error: any) {
// //       setAttemptCount(prev => prev + 1);
// //       reset();

// //       const errorCode = error.errors?.[0]?.code;

// //       if (errorCode === "verification_failed" || errorCode === "form_code_incorrect") {
// //         const remainingAttempts = 5 - (attemptCount + 1); // because we updated above
// //         if (remainingAttempts > 0) {
// //           toast.error(`Cod invalid. Mai ai ${remainingAttempts} încercări.`);
// //         } else {
// //           toast.error("Ai depășit numărul maxim de încercări. Solicită un cod nou.");
// //         }
// //       } else if (errorCode === "verification_expired") {
// //         toast.error("Codul a expirat. Te rugăm să soliciți unul nou.");
// //       } else {
// //         toast.error(getClerkErrorMessage(error));
// //       }
// //     }
// //   });
// // };

//   const onResendCode = () => {
//     if (!unverifiedEmail) return;

//     startResending(async () => {
//       try {
//         await unverifiedEmail.prepareVerification({ strategy: 'email_code' });
//         setAttemptCount(0);
//         reset();
//         toast.info("Un nou cod de verificare a fost trimis la adresa de email.");
//       } catch (error: any) {
//         console.error("Error resending verification email:", error);
//         const clerkError = error.errors?.[0];
//         toast.error(
//           clerkError?.longMessage || 
//           clerkError?.message || 
//           "A apărut o eroare la retrimiterea codului de verificare."
//         );
//       }
//     });
//   };

//   const onCancelVerification = () => {
//     if (!unverifiedEmail) return;
    
//     const confirm = window.confirm(
//       "Ești sigur că vrei să anulezi schimbarea de email? " +
//       "Vei rămâne cu adresa actuală."
//     );
//     if (!confirm) return;
    
//     startCancelling(async () => {
//       try {
//         await unverifiedEmail.destroy();
//         toast.info("Schimbarea de email a fost anulată.");
//         onVerificationCancelled?.();
//       } catch (error) {
//         toast.error("Nu s-a putut anula schimbarea.");
//       }
//     });
//   };

//   if (!unverifiedEmail) {
//     return null;
//   }

// return (
//     <Card>
//       <CardHeader>
//         <div className="flex justify-between items-start">
//           <div>
//             <CardTitle>Verificare Email</CardTitle>
//             <CardDescription className="mt-2">
//               Un cod de verificare a fost trimis la: <strong>{unverifiedEmail.emailAddress}</strong>
//               <br />
//               <span className="text-sm text-gray-500">
//                 Codul este format din 6 caractere și expiră în 10 minute.
//               </span>
//             </CardDescription>
//           </div>
//           <Button
//             variant="ghost"
//             size="icon"
//             onClick={onCancelVerification}
//             disabled={isCancelling}
//             title="Anulează schimbarea"
//           >
//             <X className="h-4 w-4" />
//           </Button>
//         </div>
//       </CardHeader>
//       <CardContent>
//         {attemptCount >= 3 && (
//           <Alert className="mb-4">
//             <AlertDescription>
//               Ai {5 - attemptCount} încercări rămase. După aceea va trebui să soliciți un cod nou.
//             </AlertDescription>
//           </Alert>
//         )}
        
//         <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
//           <div className="space-y-2">
//             <Label htmlFor="code">Cod de verificare *</Label>
//             <Input
//               id="code"
//               {...register("code")}
//               placeholder="123456"
//               className={errors.code ? "border-red-500" : ""}
//               disabled={isPending || attemptCount >= 5}
//               maxLength={10}
//             />
//             {errors.code && (
//               <p className="text-sm text-red-600">{errors.code.message}</p>
//             )}
//           </div>
//           <div className="flex justify-between">
//             <Button
//               type="button"
//               onClick={onResendCode}
//               disabled={isResending}
//               variant="outline"
//             >
//               {isResending ? (
//                 <>
//                   <Loader2 className="mr-2 h-4 w-4 animate-spin" />
//                   Se retrimite...
//                 </>
//               ) : (
//                 "Retrimite codul"
//               )}
//             </Button>

//             <Button
//               type="submit"
//               disabled={isPending || !isDirty || attemptCount >= 5}
//               className="bg-[#0066B1] hover:bg-[#004d85] text-white"
//             >
//               {isPending ? (
//                 <>
//                   <Loader2 className="mr-2 h-4 w-4 animate-spin" />
//                   Se verifică...
//                 </>
//               ) : (
//                 "Verifică codul"
//               )}
//             </Button>
//           </div>
//         </form>
//       </CardContent>
//     </Card>
//   );
// }