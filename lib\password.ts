// Password strength calculation
export const calculatePasswordStrength = (password: string): { score: number; feedback: string[] } => {
    const feedback: string[] = [];
    let score = 0;

    if (password.length >= 8) {
        score += 20;
    } else {
        feedback.push("Cel puțin 8 caractere");
    }

    if (/[a-z]/.test(password)) {
        score += 20;
    } else {
        feedback.push("O literă mică");
    }

    if (/[A-Z]/.test(password)) {
        score += 20;
    } else {
        feedback.push("O literă mare");
    }

    if (/\d/.test(password)) {
        score += 20;
    } else {
        feedback.push("O cifră");
    }

    if (/[@$!%*?&]/.test(password)) {
        score += 20;
    } else {
        feedback.push("Un caracter special (@$!%*?&)");
    }

    return { score, feedback };
};

  // Comprehensive error mapping for Clerk password errors
export const getClerkErrorMessage = (error: any): string => {
    if (!error?.errors?.[0]) {
        return "A apărut o eroare neașteptată la schimbarea parolei.";
    }

    const clerkError = error.errors[0];
    const errorMappings: Record<string, string> = {
        // Password validation errors
        'form_password_incorrect': "Parola curentă este incorectă.",
        'form_password_pwned': "Această parolă a fost compromisă într-o breșă de securitate. Alegeți o parolă diferită.",
        'form_password_too_common': "Această parolă este prea comună. Alegeți o parolă mai sigură.",
        'form_password_length_too_short': "Parola este prea scurtă. Minimum 8 caractere.",
        'form_password_length_too_long': "Parola este prea lungă. Maximum 128 caractere.",
        'form_password_no_uppercase': "Parola trebuie să conțină cel puțin o literă mare.",
        'form_password_no_lowercase': "Parola trebuie să conțină cel puțin o literă mică.",
        'form_password_no_numbers': "Parola trebuie să conțină cel puțin o cifră.",
        'form_password_no_special_char': "Parola trebuie să conțină cel puțin un caracter special.",
        'form_password_same_as_current': "Parola nouă trebuie să fie diferită de cea curentă.",
        'form_password_requires_verification': "Din motive de securitate, trebuie să vă confirmați identitatea. Vă rugăm să vă deconectați și să vă conectați din nou pentru a schimba parola.",
        'form_identifier_exists' : "Această adresă de email este deja folosită.",
        'form_param_format_invalid': "Formatul datelor este invalid.",
        'form_phone_number_exists' : "Acest număr de telefon este deja asociat contului dvs.",
        'phone_number_exists' : "Acest număr de telefon este deja asociat altui cont.",
        'form_phone_number_invalid' : "Numărul de telefon este invalid.",
        'not_allowed_access' : "Nu aveți permisiunea de a efectua această acțiune.",
        'unsupported_country_code' : "Codul de țară nu este suportat.",
        'form_identifier_not_found' : "Adresa de email nu a fost găsită.",
        'email_address_not_found' : "Adresa de email nu a fost găsită.",
        'form_param_nil' : "Parametrul este obligatoriu.",
        'form_param_missing' : "Parametrul este obligatoriu.",
        'form_param_unknown' : "Parametrul este necunoscut.",
        

        // Rate limiting and security
        'rate_limit_exceeded': "Prea multe încercări. Vă rugăm să așteptați câteva minute înainte să încercați din nou.",
        'session_invalid': "Sesiunea dvs. a expirat. Vă rugăm să vă reconectați.",
        'user_locked': "Contul dvs. a fost temporar blocat din motive de securitate.",

        // Network and server errors
        'network_error': "Eroare de conexiune. Verificați conexiunea la internet și încercați din nou.",
        'server_error': "Eroare de server. Vă rugăm să încercați din nou în câteva momente.",

        // Generic fallbacks
        'form_password_validation_failed': "Parola curenta este incorecta. Vă rugăm să încercați din nou.",
        'authentication_invalid': "Autentificarea a eșuat. Vă rugăm să vă reconectați.",

        // Verification errors
        'verification_required': "Este necesară o verificare suplimentară din motive de securitate.",
        'verification_failed': "Verificarea a eșuat. Vă rugăm să încercați din nou.",
        'session_verification_required': "Sesiunea necesită verificare suplimentară.",
        'session_reverification_required': "Sesiunea necesită o re-verificare. Te rugam sa te deconectezi si sa te reconectezi.",
        'verification_expired': "Verificarea a expirat. Vă rugăm să încercați din nou.",
        'verification_code_too_many_requests': "Prea multe încercări de verificare. Vă rugăm să așteptați câteva minute înainte să încercați din nou.",
        'verification_already_verified': "Adresa de email este deja verificată.",
        'verification_missing': "Verificarea nu a fost găsită.",
        'verification_not_sent': "Verificarea nu a putut fi trimisă. Vă rugăm să încercați din nou.",
        'verification_status_unknown': "Starea verificării este necunoscută.",
        'form_code_incorrect': "Codul de verificare este incorect. Vă rugăm să încercați din nou.",
    };

    return errorMappings[clerkError.code] || 
            clerkError.longMessage || 
            clerkError.message || 
            "A apărut o eroare la schimbarea parolei. Vă rugăm să încercați din nou.";
};

export const getStrengthText = (score: number) => {
    if (score < 40) return "Slabă";
    if (score < 60) return "Medie";
    if (score < 80) return "Bună";
    return "Foarte bună";
};

export const getStrengthColor = (score: number): string => {
  if (score < 40) return "bg-red-500";
  if (score < 60) return "bg-orange-500";
  if (score < 80) return "bg-yellow-500";
  return "bg-green-500";
};



